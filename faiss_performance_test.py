#!/usr/bin/env python3
"""
FAISS高强度性能测试
专门设计用于性能分析，减少Python开销，突出FAISS计算
"""

import faiss
import numpy as np
import time
import sys

def intensive_faiss_test():
    print("🚀 FAISS高强度性能测试")
    print(f"FAISS版本: {faiss.__version__}")
    
    # 使用更大的数据集来突出FAISS计算
    d = 256        # 更高维度
    nb = 1000000   # 100万个向量
    nq = 10000     # 1万个查询
    k = 100        # 更多的近邻
    
    print(f"📊 数据规模: {nb:,}个{d}维向量, {nq:,}个查询, top-{k}")
    
    # 生成数据
    print("生成测试数据...")
    np.random.seed(1234)
    xb = np.random.random((nb, d)).astype('float32')
    xq = np.random.random((nq, d)).astype('float32')
    
    # 归一化数据（更真实的场景）
    faiss.normalize_L2(xb)
    faiss.normalize_L2(xq)
    
    print("🔥 开始高强度计算测试...")
    
    # 测试1: IndexFlatIP (内积搜索，计算密集)
    print("\n=== 测试1: IndexFlatIP (内积搜索) ===")
    index_flat = faiss.IndexFlatIP(d)
    index_flat.add(xb)
    
    print("执行大批量搜索...")
    start_time = time.time()
    
    # 连续多次搜索以增加计算量
    for i in range(5):
        D, I = index_flat.search(xq, k)
    
    flat_time = time.time() - start_time
    print(f"IndexFlatIP总时间: {flat_time:.3f}s")
    print(f"平均每次搜索: {flat_time/5:.3f}s")
    print(f"QPS: {nq*5/flat_time:.1f}")
    
    # 测试2: IndexIVFFlat (更复杂的索引)
    print("\n=== 测试2: IndexIVFFlat ===")
    nlist = 1000
    quantizer = faiss.IndexFlatIP(d)
    index_ivf = faiss.IndexIVFFlat(quantizer, d, nlist)
    
    print("训练IVF索引...")
    index_ivf.train(xb)
    print("添加向量到IVF索引...")
    index_ivf.add(xb)
    
    # 设置搜索参数
    index_ivf.nprobe = 50
    
    print("执行IVF搜索...")
    start_time = time.time()
    
    for i in range(3):
        D, I = index_ivf.search(xq, k)
    
    ivf_time = time.time() - start_time
    print(f"IndexIVFFlat总时间: {ivf_time:.3f}s")
    print(f"平均每次搜索: {ivf_time/3:.3f}s")
    print(f"QPS: {nq*3/ivf_time:.1f}")
    
    # 测试3: 距离计算密集测试
    print("\n=== 测试3: 纯距离计算 ===")
    start_time = time.time()
    
    # 直接调用FAISS的距离计算函数
    for i in range(10):
        distances = faiss.pairwise_distances(xq[:1000], xb[:10000])
    
    dist_time = time.time() - start_time
    print(f"距离计算时间: {dist_time:.3f}s")
    
    print(f"\n✅ 高强度测试完成!")
    print(f"总计算时间: {flat_time + ivf_time + dist_time:.3f}s")

if __name__ == "__main__":
    print("=" * 60)
    intensive_faiss_test()
    print("=" * 60)
    print("🔥 现在运行性能分析:")
    print("   perf record -g python3 faiss_performance_test.py")
