#!/usr/bin/env python3
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

"""
Benchmark script for Cohere 10M vectors with pre-built FAISS index support.
Supports time-limited benchmarking and custom query datasets.

Usage:
    python bench_cohere_10m.py --index_path /path/to/index.faiss --query_path /path/to/queries.npy --duration 30
"""

import argparse
import time
import sys
import os
import numpy as np
import faiss
from typing import Optional, Tuple, Dict, Any
import threading
import signal
import psutil
import resource
import multiprocessing
from contextlib import contextmanager


class TimeoutException(Exception):
    """Exception raised when benchmark times out"""
    pass


class ResourceLimitException(Exception):
    """Exception raised when resource limits are exceeded"""
    pass


class ResourceManager:
    """Manages CPU and memory resource limits for FAISS benchmarks"""

    def __init__(self, max_cpu_cores: int = 16, max_memory_gb: float = 64.0):
        """
        Initialize resource manager

        Args:
            max_cpu_cores: Maximum number of CPU cores to use
            max_memory_gb: Maximum memory in GB
        """
        self.max_cpu_cores = max_cpu_cores
        self.max_memory_bytes = max_memory_gb * 1024 * 1024 * 1024
        self.process = psutil.Process()
        self.initial_memory = self.get_memory_usage()
        self.monitoring = False
        self.monitor_thread = None

        # Set CPU affinity if possible
        try:
            available_cores = list(range(multiprocessing.cpu_count()))
            if len(available_cores) > max_cpu_cores:
                # Use first N cores
                cores_to_use = available_cores[:max_cpu_cores]
                self.process.cpu_affinity(cores_to_use)
                print(f"Set CPU affinity to cores: {cores_to_use}")
            else:
                print(f"Using all available cores: {available_cores}")
        except (AttributeError, OSError) as e:
            print(f"Warning: Could not set CPU affinity: {e}")

        # Set FAISS thread limit
        faiss.omp_set_num_threads(min(max_cpu_cores, faiss.omp_get_max_threads()))
        print(f"Set FAISS OpenMP threads to: {faiss.omp_get_max_threads()}")

    def get_memory_usage(self) -> float:
        """Get current memory usage in bytes"""
        return self.process.memory_info().rss

    def get_memory_usage_gb(self) -> float:
        """Get current memory usage in GB"""
        return self.get_memory_usage() / (1024 * 1024 * 1024)

    def get_cpu_usage(self) -> float:
        """Get current CPU usage percentage (normalized by number of cores)"""
        # Get CPU usage and normalize by the number of available cores
        cpu_percent = self.process.cpu_percent()
        # Normalize to per-core percentage
        return cpu_percent / self.max_cpu_cores if self.max_cpu_cores > 0 else cpu_percent

    def check_memory_limit(self) -> bool:
        """Check if memory usage is within limits"""
        current_memory = self.get_memory_usage()
        return current_memory <= self.max_memory_bytes

    def get_resource_stats(self) -> Dict[str, Any]:
        """Get current resource usage statistics"""
        memory_gb = self.get_memory_usage_gb()
        cpu_percent = self.get_cpu_usage()

        return {
            'memory_gb': memory_gb,
            'memory_limit_gb': self.max_memory_bytes / (1024 * 1024 * 1024),
            'memory_usage_percent': (memory_gb / (self.max_memory_bytes / (1024 * 1024 * 1024))) * 100,
            'cpu_percent': cpu_percent,
            'cpu_cores_limit': self.max_cpu_cores,
            'faiss_threads': faiss.omp_get_max_threads()
        }

    def start_monitoring(self, check_interval: float = 1.0):
        """Start background resource monitoring"""
        if self.monitoring:
            return

        self.monitoring = True

        def monitor():
            while self.monitoring:
                if not self.check_memory_limit():
                    memory_gb = self.get_memory_usage_gb()
                    limit_gb = self.max_memory_bytes / (1024 * 1024 * 1024)
                    print(f"WARNING: Memory limit exceeded! Using {memory_gb:.2f}GB > {limit_gb:.2f}GB")
                    # Could raise exception here if strict enforcement is needed
                    # raise ResourceLimitException(f"Memory limit exceeded: {memory_gb:.2f}GB > {limit_gb:.2f}GB")

                time.sleep(check_interval)

        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
        print(f"Started resource monitoring (check interval: {check_interval}s)")

    def stop_monitoring(self):
        """Stop background resource monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        print("Stopped resource monitoring")

    def print_resource_summary(self):
        """Print current resource usage summary"""
        stats = self.get_resource_stats()
        print("\n" + "="*50)
        print("RESOURCE USAGE SUMMARY")
        print("="*50)
        print(f"Memory Usage: {stats['memory_gb']:.2f}GB / {stats['memory_limit_gb']:.2f}GB ({stats['memory_usage_percent']:.1f}%)")
        print(f"CPU Usage: {stats['cpu_percent']:.1f}%")
        print(f"CPU Cores Limit: {stats['cpu_cores_limit']}")
        print(f"FAISS Threads: {stats['faiss_threads']}")
        print("="*50)


@contextmanager
def resource_limited_execution(max_cpu_cores: int = 16, max_memory_gb: float = 64.0):
    """Context manager for resource-limited execution"""
    resource_manager = ResourceManager(max_cpu_cores, max_memory_gb)
    resource_manager.start_monitoring()

    try:
        yield resource_manager
    finally:
        resource_manager.stop_monitoring()
        resource_manager.print_resource_summary()


class TimeLimitedBenchmark:
    """Context manager for time-limited benchmarking"""
    
    def __init__(self, duration_seconds: float):
        self.duration = duration_seconds
        self.start_time = None
        self.timeout_flag = threading.Event()
        self.timer = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.timer = threading.Timer(self.duration, self._timeout)
        self.timer.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.timer:
            self.timer.cancel()
    
    def _timeout(self):
        self.timeout_flag.set()
    
    def is_timeout(self) -> bool:
        return self.timeout_flag.is_set()
    
    def elapsed_time(self) -> float:
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time


class CohereDataset:
    """Dataset class for Cohere 10M vectors"""
    
    def __init__(self, query_path: Optional[str] = None, 
                 query_vectors: Optional[np.ndarray] = None,
                 ground_truth: Optional[np.ndarray] = None):
        """
        Initialize Cohere dataset
        
        Args:
            query_path: Path to query vectors (.npy file)
            query_vectors: Query vectors as numpy array
            ground_truth: Ground truth nearest neighbors
        """
        self.d = 768  # Cohere embedding dimension
        self.metric = 'L2'  # Default metric
        
        if query_path is not None:
            self.xq = self._load_queries(query_path)
        elif query_vectors is not None:
            self.xq = query_vectors.astype('float32')
        else:
            # Generate synthetic queries if none provided
            print("No queries provided, generating synthetic queries...")
            self.xq = self._generate_synthetic_queries(1000)
        
        self.nq = len(self.xq)
        self.gt = ground_truth
        
        print(f"Loaded {self.nq} queries with dimension {self.d}")
    
    def _load_queries(self, query_path: str) -> np.ndarray:
        """Load query vectors from file"""
        if not os.path.exists(query_path):
            raise FileNotFoundError(f"Query file not found: {query_path}")
        
        print(f"Loading queries from {query_path}")
        queries = np.load(query_path)
        
        if queries.dtype != np.float32:
            queries = queries.astype('float32')
        
        if len(queries.shape) != 2:
            raise ValueError(f"Expected 2D array, got shape {queries.shape}")
        
        if queries.shape[1] != self.d:
            raise ValueError(f"Expected dimension {self.d}, got {queries.shape[1]}")
        
        return queries
    
    def _generate_synthetic_queries(self, nq: int) -> np.ndarray:
        """Generate synthetic query vectors for testing"""
        np.random.seed(42)  # For reproducibility
        return np.random.randn(nq, self.d).astype('float32')
    
    def get_queries(self) -> np.ndarray:
        """Return query vectors"""
        return self.xq
    
    def get_groundtruth(self, k: Optional[int] = None) -> Optional[np.ndarray]:
        """Return ground truth if available"""
        if self.gt is None:
            return None
        if k is not None and k < self.gt.shape[1]:
            return self.gt[:, :k]
        return self.gt


def load_prebuilt_index(index_path: str) -> faiss.Index:
    """
    Load a pre-built FAISS index from file
    
    Args:
        index_path: Path to the FAISS index file
        
    Returns:
        Loaded FAISS index
    """
    if not os.path.exists(index_path):
        raise FileNotFoundError(f"Index file not found: {index_path}")
    
    print(f"Loading pre-built index from {index_path}")
    start_time = time.time()
    
    # Load with memory mapping for large indices
    io_flag = faiss.IO_FLAG_READ_ONLY | faiss.IO_FLAG_MMAP
    index = faiss.read_index(index_path, io_flag)
    
    load_time = time.time() - start_time
    print(f"Index loaded in {load_time:.2f} seconds")
    print(f"Index type: {type(index).__name__}")
    print(f"Index size: {index.ntotal} vectors")
    print(f"Index dimension: {index.d}")

    # Print HNSW parameters if it's an HNSW index
    if hasattr(index, 'hnsw'):
        hnsw = index.hnsw
        print(f"HNSW Parameters:")

        # Get M from the neighbor structure
        try:
            if hasattr(hnsw, 'cum_nneighbor_per_level') and hnsw.cum_nneighbor_per_level.size() > 1:
                # M is typically half of the connections at level 0
                level0_connections = hnsw.cum_nneighbor_per_level.at(1) - hnsw.cum_nneighbor_per_level.at(0)
                M = level0_connections // 2
                print(f"  M (connections per node): {M}")
        except Exception as e:
            print(f"  M: Unable to determine ({e})")

        if hasattr(hnsw, 'efConstruction'):
            print(f"  efConstruction: {hnsw.efConstruction}")

        if hasattr(hnsw, 'efSearch'):
            print(f"  efSearch: {hnsw.efSearch}")

        if hasattr(hnsw, 'max_level'):
            print(f"  max_level: {hnsw.max_level}")

        if hasattr(hnsw, 'entry_point'):
            print(f"  entry_point: {hnsw.entry_point}")

        if hasattr(hnsw, 'ntotal'):
            print(f"  ntotal: {hnsw.ntotal}")

        # Print level distribution
        try:
            if hasattr(hnsw, 'levels') and hnsw.levels.size() > 0:
                levels_list = [hnsw.levels.at(i) for i in range(min(1000, hnsw.levels.size()))]  # Sample first 1000
                max_level = max(levels_list) if levels_list else 0
                print(f"  levels: 0 to {max_level}")
                level_counts = {}
                for level in levels_list:
                    level_counts[level] = level_counts.get(level, 0) + 1
                print(f"  level distribution (sample): {dict(sorted(level_counts.items()))}")
        except Exception as e:
            print(f"  levels: Unable to determine ({e})")

    return index


def evaluate_search_performance(index: faiss.Index, 
                               queries: np.ndarray,
                               k: int,
                               ground_truth: Optional[np.ndarray] = None,
                               search_params: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
    """
    Evaluate search performance for given queries
    
    Args:
        index: FAISS index
        queries: Query vectors
        k: Number of nearest neighbors to retrieve
        ground_truth: Ground truth for recall calculation
        search_params: Search parameters (e.g., nprobe, efSearch)
        
    Returns:
        Dictionary with performance metrics
    """
    nq = len(queries)
    
    # Set search parameters if provided
    if search_params:
        for param, value in search_params.items():
            try:
                if hasattr(index, param):
                    setattr(index, param, value)
                elif hasattr(index, 'hnsw') and hasattr(index.hnsw, param):
                    setattr(index.hnsw, param, value)
                elif hasattr(index, 'quantizer') and hasattr(index.quantizer, param):
                    setattr(index.quantizer, param, value)
                else:
                    print(f"Warning: Parameter {param} not found in index")
            except Exception as e:
                print(f"Warning: Failed to set parameter {param}={value}: {e}")
    
    # Perform search
    start_time = time.time()
    distances, indices = index.search(queries, k)
    search_time = time.time() - start_time
    
    # Calculate metrics
    metrics = {
        'search_time_ms': search_time * 1000,
        'queries_per_second': nq / search_time,
        'ms_per_query': (search_time * 1000) / nq,
        'total_queries': nq
    }
    
    # Calculate recall if ground truth is available
    if ground_truth is not None:
        recall_at_1 = np.mean(indices[:, 0:1] == ground_truth[:, 0:1])
        recall_at_k = np.mean([
            len(np.intersect1d(indices[i], ground_truth[i])) / k
            for i in range(min(nq, len(ground_truth)))
        ])
        metrics['recall_at_1'] = recall_at_1
        metrics[f'recall_at_{k}'] = recall_at_k
    
    # Calculate missing rate
    missing_rate = np.mean(indices == -1)
    metrics['missing_rate'] = missing_rate
    
    return metrics


def run_time_limited_benchmark(index: faiss.Index,
                              dataset: CohereDataset,
                              duration_seconds: float,
                              k: int = 10,
                              search_params_list: Optional[list] = None,
                              resource_manager: Optional[ResourceManager] = None,
                              quiet: bool = False) -> Dict[str, Any]:
    """
    Run time-limited benchmark with multiple search parameter configurations

    Args:
        index: FAISS index
        dataset: Dataset containing queries
        duration_seconds: Maximum duration for benchmark
        k: Number of nearest neighbors to retrieve
        search_params_list: List of search parameter configurations to test

    Returns:
        Dictionary with benchmark results
    """
    if search_params_list is None:
        # Default HNSW search parameters
        search_params_list = [
            {'efSearch': 16},
            {'efSearch': 32},
            {'efSearch': 64},
            {'efSearch': 128},
            {'efSearch': 256}
        ]

    queries = dataset.get_queries()
    ground_truth = dataset.get_groundtruth(k)

    results = {
        'total_duration': duration_seconds,
        'configurations': [],
        'summary': {},
        'resource_usage': []
    }

    print(f"Starting time-limited benchmark for {duration_seconds} seconds")
    print(f"Testing {len(search_params_list)} parameter configurations")
    print(f"Using {len(queries)} queries with k={k}")

    # Print initial resource usage
    if resource_manager:
        print(f"Resource limits: {resource_manager.max_cpu_cores}C / {resource_manager.max_memory_bytes/(1024**3):.1f}GB")
        initial_stats = resource_manager.get_resource_stats()
        print(f"Initial memory usage: {initial_stats['memory_gb']:.2f}GB")

    start_time = time.time()

    # Run each configuration multiple times until time limit is reached
    config_cycle = 0
    total_runs = 0

    while time.time() - start_time < duration_seconds:
        for i, search_params in enumerate(search_params_list):
            current_time = time.time() - start_time
            if current_time >= duration_seconds:
                print(f"\nTime limit reached ({current_time:.2f}s), stopping benchmark")
                break

            if config_cycle == 0:  # First cycle, print configuration info
                print(f"\nConfiguration {i+1}/{len(search_params_list)}: {search_params}")

            try:
                # Record resource usage before search
                if resource_manager:
                    pre_search_stats = resource_manager.get_resource_stats()

                # Run evaluation
                metrics = evaluate_search_performance(
                    index, queries, k, ground_truth, search_params
                )

                # Record resource usage after search (only for first cycle and every 50 cycles)
                if resource_manager and (config_cycle == 0 or config_cycle % 50 == 0):
                    post_search_stats = resource_manager.get_resource_stats()
                    resource_usage = {
                        'config_id': i,
                        'cycle': config_cycle,
                        'run_id': total_runs,
                        'pre_search': pre_search_stats,
                        'post_search': post_search_stats,
                        'memory_delta_gb': post_search_stats['memory_gb'] - pre_search_stats['memory_gb']
                    }
                    results['resource_usage'].append(resource_usage)

                config_result = {
                    'config_id': i,
                    'cycle': config_cycle,
                    'run_id': total_runs,
                    'search_params': search_params,
                    'metrics': metrics,
                    'elapsed_time': current_time
                }

                results['configurations'].append(config_result)
                total_runs += 1

                # Print results only for first cycle or every 50 cycles (unless quiet mode)
                if not quiet and (config_cycle == 0 or config_cycle % 50 == 0):
                    print(f"  Cycle {config_cycle}: QPS: {metrics['queries_per_second']:.2f}, "
                          f"ms/query: {metrics['ms_per_query']:.3f}")

            except Exception as e:
                print(f"Error in configuration {i}, cycle {config_cycle}: {e}")
                continue

        config_cycle += 1
        if not quiet:
            if config_cycle == 1:
                print(f"\nCompleted first cycle, continuing for {duration_seconds - (time.time() - start_time):.1f} more seconds...")
            elif config_cycle % 50 == 0:
                print(f"Completed {config_cycle} cycles, {total_runs} total runs...")
        elif quiet and config_cycle % 100 == 0:
            # In quiet mode, only print every 100 cycles
            print(f"Progress: {config_cycle} cycles, {total_runs} total runs...")

        # Break if time limit reached
        if time.time() - start_time >= duration_seconds:
            break

    # Calculate summary statistics
    actual_duration = time.time() - start_time
    if results['configurations']:
        all_qps = [config['metrics']['queries_per_second'] for config in results['configurations']]
        all_recall_1 = [config['metrics'].get('recall_at_1', 0) for config in results['configurations']]

        # Calculate overall QPS based on total queries and total time
        total_queries_processed = len(results['configurations']) * len(queries)
        overall_qps = total_queries_processed / actual_duration

        # Group by configuration to get average performance per config
        config_groups = {}
        for config in results['configurations']:
            config_id = config['config_id']
            if config_id not in config_groups:
                config_groups[config_id] = []
            config_groups[config_id].append(config['metrics']['queries_per_second'])

        avg_qps_per_config = {cid: sum(qps_list)/len(qps_list) for cid, qps_list in config_groups.items()}

        results['summary'] = {
            'total_runs': len(results['configurations']),
            'total_cycles': config_cycle,
            'configurations_tested': len(config_groups),
            'max_qps': max(all_qps),
            'min_qps': min(all_qps),
            'avg_qps_individual_runs': sum(all_qps) / len(all_qps),
            'overall_qps': overall_qps,
            'total_queries_processed': total_queries_processed,
            'avg_qps_per_config': avg_qps_per_config,
            'max_recall_at_1': max(all_recall_1) if all_recall_1 and all_recall_1[0] != 0 else None,
            'actual_duration': actual_duration
        }

    return results


def print_resource_usage_summary(resource_usage_list: list):
    """Print resource usage summary"""
    if not resource_usage_list:
        return

    print("\n" + "="*60)
    print("RESOURCE USAGE SUMMARY")
    print("="*60)

    # Group by config_id and get statistics
    config_stats = {}
    for usage in resource_usage_list:
        config_id = usage['config_id']
        if config_id not in config_stats:
            config_stats[config_id] = {
                'cpu_usage': [],
                'memory_usage': [],
                'memory_delta': []
            }

        config_stats[config_id]['cpu_usage'].append(usage['post_search']['cpu_percent'])
        config_stats[config_id]['memory_usage'].append(usage['post_search']['memory_gb'])
        config_stats[config_id]['memory_delta'].append(usage['memory_delta_gb'])

    # Print summary statistics
    for config_id, stats in config_stats.items():
        avg_cpu = sum(stats['cpu_usage']) / len(stats['cpu_usage'])
        avg_memory = sum(stats['memory_usage']) / len(stats['memory_usage'])
        max_memory_delta = max(stats['memory_delta'])

        print(f"Config {config_id + 1}:")
        print(f"  Average CPU: {avg_cpu:.1f}%")
        print(f"  Average Memory: {avg_memory:.2f}GB")
        print(f"  Max Memory Delta: {max_memory_delta:+.3f}GB")
        print(f"  Samples: {len(stats['cpu_usage'])}")


def print_benchmark_summary(results: Dict[str, Any]):
    """Print a summary of benchmark results"""
    print("\n" + "="*60)
    print("BENCHMARK SUMMARY")
    print("="*60)

    summary = results['summary']
    print(f"Total runs: {summary['total_runs']}")
    print(f"Total cycles: {summary['total_cycles']}")
    print(f"Configurations tested: {summary['configurations_tested']}")
    print(f"Actual duration: {summary['actual_duration']:.2f} seconds")
    print(f"Total queries processed: {summary['total_queries_processed']:,}")
    print(f"Overall QPS: {summary['overall_qps']:.2f}")
    print(f"QPS range (individual runs): {summary['min_qps']:.2f} - {summary['max_qps']:.2f}")
    print(f"Average QPS (individual runs): {summary['avg_qps_individual_runs']:.2f}")

    if summary.get('max_recall_at_1') is not None:
        print(f"Max Recall@1: {summary['max_recall_at_1']:.4f}")

    print("\nAverage Performance per Configuration:")
    print("-" * 60)
    for config_id, avg_qps in summary['avg_qps_per_config'].items():
        # Get the first config with this ID to show parameters
        first_config = next(c for c in results['configurations'] if c['config_id'] == config_id)
        params_str = ", ".join([f"{k}={v}" for k, v in first_config['search_params'].items()])
        print(f"Config {config_id+1}: {params_str}")
        print(f"  Average QPS: {avg_qps:.2f}")

        # Count runs for this config
        runs_count = len([c for c in results['configurations'] if c['config_id'] == config_id])
        print(f"  Runs: {runs_count}")

    print(f"\nNote: Each configuration was run multiple times over {summary['actual_duration']:.1f} seconds")


def main():
    """Main function to run the benchmark"""
    parser = argparse.ArgumentParser(
        description="Benchmark Cohere 10M vectors with pre-built FAISS index"
    )

    parser.add_argument(
        '--index_path',
        type=str,
        required=True,
        help='Path to pre-built FAISS index file'
    )

    parser.add_argument(
        '--query_path',
        type=str,
        help='Path to query vectors (.npy file). If not provided, synthetic queries will be generated'
    )

    parser.add_argument(
        '--ground_truth_path',
        type=str,
        help='Path to ground truth file (.npy file)'
    )

    parser.add_argument(
        '--duration',
        type=float,
        default=30.0,
        help='Benchmark duration in seconds (default: 30)'
    )

    parser.add_argument(
        '--k',
        type=int,
        default=10,
        help='Number of nearest neighbors to retrieve (default: 10)'
    )

    parser.add_argument(
        '--num_threads',
        type=int,
        default=-1,
        help='Number of OpenMP threads (-1 for default)'
    )

    parser.add_argument(
        '--ef_search_values',
        type=str,
        default="16,32,64,128,256",
        help='Comma-separated efSearch values to test (default: 16,32,64,128,256)'
    )

    parser.add_argument(
        '--custom_search_params',
        type=str,
        help='Custom search parameters in JSON format, e.g., \'[{"efSearch": 100, "nprobe": 32}]\''
    )

    parser.add_argument(
        '--max_cpu_cores',
        type=int,
        default=16,
        help='Maximum number of CPU cores to use (default: 16)'
    )

    parser.add_argument(
        '--max_memory_gb',
        type=float,
        default=64.0,
        help='Maximum memory usage in GB (default: 64.0)'
    )

    parser.add_argument(
        '--enable_resource_monitoring',
        action='store_true',
        help='Enable real-time resource monitoring'
    )

    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Reduce output verbosity (less frequent progress updates)'
    )

    args = parser.parse_args()

    # Set number of threads
    if args.num_threads > 0:
        faiss.omp_set_num_threads(args.num_threads)
        print(f"Set OpenMP threads to {args.num_threads}")
    else:
        print(f"Using default OpenMP threads: {faiss.omp_get_max_threads()}")

    try:
        # Use resource-limited execution context
        with resource_limited_execution(
            max_cpu_cores=args.max_cpu_cores,
            max_memory_gb=args.max_memory_gb
        ) as resource_manager:

            # Load index
            print(f"Loading index with resource limits: {args.max_cpu_cores}C / {args.max_memory_gb}GB")
            index = load_prebuilt_index(args.index_path)

            # Check memory usage after loading index
            stats_after_load = resource_manager.get_resource_stats()
            print(f"Memory usage after loading index: {stats_after_load['memory_gb']:.2f}GB")

            # Load ground truth if provided
            ground_truth = None
            if args.ground_truth_path:
                if os.path.exists(args.ground_truth_path):
                    print(f"Loading ground truth from {args.ground_truth_path}")
                    ground_truth = np.load(args.ground_truth_path)
                else:
                    print(f"Warning: Ground truth file not found: {args.ground_truth_path}")

            # Create dataset
            dataset = CohereDataset(
                query_path=args.query_path,
                ground_truth=ground_truth
            )

            # Parse search parameters
            if args.custom_search_params:
                import json
                try:
                    search_params_list = json.loads(args.custom_search_params)
                    print(f"Using custom search parameters: {search_params_list}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing custom search parameters: {e}")
                    print("Using default efSearch values instead")
                    ef_search_values = [int(x.strip()) for x in args.ef_search_values.split(',')]
                    search_params_list = [{'efSearch': ef} for ef in ef_search_values]
            else:
                ef_search_values = [int(x.strip()) for x in args.ef_search_values.split(',')]
                search_params_list = [{'efSearch': ef} for ef in ef_search_values]

            # Run benchmark with resource monitoring
            results = run_time_limited_benchmark(
                index=index,
                dataset=dataset,
                duration_seconds=args.duration,
                k=args.k,
                search_params_list=search_params_list,
                resource_manager=resource_manager if args.enable_resource_monitoring else None,
                quiet=args.quiet
            )

            # Print summary
            print_benchmark_summary(results)

            # Print resource usage summary if monitoring was enabled
            if args.enable_resource_monitoring and results.get('resource_usage'):
                print_resource_usage_summary(results['resource_usage'])

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
