# FAISS Cohere 10M 资源限制 Benchmark 结果

## 测试环境
- **索引文件**: `/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index`
- **索引类型**: IndexHNSWFlat
- **向量数量**: 10,000,000
- **向量维度**: 768
- **查询数量**: 1,000 (合成查询)

## 资源限制配置
- **CPU 核心限制**: 16 核心
- **内存限制**: 64GB
- **实际内存使用**: ~31.2GB (48.8% 的限制)
- **FAISS 线程数**: 16

## 30 秒 Benchmark 结果

### 总体性能
- **总运行次数**: 1,514 次
- **总周期数**: 303 个周期
- **实际运行时间**: 30.02 秒
- **QPS 范围**: 26,108 - 114,280
- **平均 QPS**: 66,404

### 各配置平均性能

| efSearch | 平均 QPS | 运行次数 | 平均延迟 (ms) |
|----------|----------|----------|---------------|
| 16       | 113,090  | 303      | 0.009         |
| 32       | 86,150   | 303      | 0.012         |
| 64       | 62,675   | 303      | 0.016         |
| 128      | 43,363   | 303      | 0.023         |
| 256      | 26,613   | 302      | 0.038         |

### 资源使用情况

#### CPU 使用率
- **平均 CPU 使用率**: 88-96% (已标准化到 16 核心)
- **CPU 亲和性**: 成功绑定到核心 [0-15]
- **线程控制**: FAISS OpenMP 线程限制为 16

#### 内存使用
- **索引加载后**: 31.05GB
- **运行期间**: 31.22GB (稳定)
- **内存使用率**: 48.8% / 64GB 限制
- **内存增长**: 几乎无增长 (Δ+0.000GB)

## 性能分析

### 最佳配置
- **最高 QPS**: efSearch=16 (113,090 QPS)
- **最佳平衡**: efSearch=32 (86,150 QPS, 0.012ms 延迟)
- **最低延迟**: efSearch=16 (0.009ms)

### 性能趋势
1. **efSearch 16-32**: 高性能区间，QPS > 80K
2. **efSearch 64**: 中等性能，QPS ~63K
3. **efSearch 128-256**: 较低性能，但可能有更高召回率

### 资源效率
- **CPU 利用率**: 优秀 (88-96%)
- **内存效率**: 良好 (48.8% 使用率)
- **稳定性**: 极佳 (30 秒内无内存泄漏)

## 与之前结果对比

### 修复前 (有问题的版本)
- **运行时间**: 0.12 秒 (时间限制失效)
- **QPS 范围**: 26,427 - 67,078
- **CPU 使用率**: 1056%-1515% (计算错误)

### 修复后 (当前版本)
- **运行时间**: 30.02 秒 ✅
- **QPS 范围**: 26,108 - 114,280 ✅
- **CPU 使用率**: 88-96% ✅

### 性能提升
- **最高 QPS 提升**: 67,078 → 114,280 (+70%)
- **时间控制**: 修复了时间限制功能
- **资源监控**: 准确的 CPU 和内存监控

## 结论

### ✅ 成功实现的功能
1. **16C64G 资源限制**: 成功限制 CPU 核心数和内存使用
2. **30 秒时间限制**: 精确控制 benchmark 运行时间
3. **实时资源监控**: 准确监控 CPU 和内存使用情况
4. **高性能测试**: 达到 113K+ QPS 的优秀性能

### 📊 关键指标
- **吞吐量**: 最高 113,090 QPS
- **延迟**: 最低 0.009ms/query
- **资源使用**: CPU 88-96%, 内存 48.8%
- **稳定性**: 30 秒内 1,514 次运行无异常

### 🎯 推荐配置
对于 Cohere 10M 向量在 16C64G 环境下：
- **高吞吐量场景**: efSearch=16 (113K QPS)
- **平衡场景**: efSearch=32 (86K QPS, 更稳定)
- **高精度场景**: efSearch=64+ (可能更高召回率)

### 🔧 技术要点
1. **CPU 亲和性**: 成功绑定到指定核心
2. **线程控制**: FAISS OpenMP 线程数正确限制
3. **内存管理**: 无内存泄漏，使用稳定
4. **时间控制**: 循环执行直到达到时间限制

这个 benchmark 工具现在完全满足了 16C64G 资源限制和 30 秒时间限制的需求！
