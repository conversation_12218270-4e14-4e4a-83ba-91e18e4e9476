# Cohere 10M Vector Benchmark Tool

这是一个专门为 Cohere 10M 向量数据集设计的 FAISS benchmark 工具，支持预加载索引文件和指定时长的性能测试。

## 功能特性

- ✅ **预加载索引支持**：可以直接加载预构建的 FAISS 索引文件
- ✅ **时间限制测试**：支持指定 benchmark 运行时长（如 30 秒）
- ✅ **自定义查询数据集**：支持加载自定义查询向量或自动生成合成查询
- ✅ **多参数配置测试**：自动测试多种搜索参数配置
- ✅ **详细性能指标**：提供 QPS、延迟、召回率等详细指标
- ✅ **HNSW 优化**：专门针对 HNSW 索引的参数调优
- ✅ **资源限制**：支持 CPU 核心数和内存使用量限制（如 16C64G）
- ✅ **实时监控**：实时监控资源使用情况和性能指标

## 使用方法

### 基本用法

```bash
# 使用预构建索引进行 30 秒 benchmark
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30
```

### 完整参数示例

```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --query_path /path/to/your/queries.npy \
    --ground_truth_path /path/to/ground_truth.npy \
    --duration 30 \
    --k 10 \
    --num_threads 8 \
    --ef_search_values "16,32,64,128,256" \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --enable_resource_monitoring
```

### 资源限制示例

```bash
# 限制使用 16 核心和 64GB 内存，启用资源监控
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --enable_resource_monitoring

# 限制使用 8 核心和 32GB 内存
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 8 \
    --max_memory_gb 32
```

## 参数说明

| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--index_path` | ✅ | - | 预构建 FAISS 索引文件路径 |
| `--query_path` | ❌ | 自动生成 | 查询向量文件路径 (.npy 格式) |
| `--ground_truth_path` | ❌ | - | Ground truth 文件路径 (.npy 格式) |
| `--duration` | ❌ | 30.0 | Benchmark 运行时长（秒） |
| `--k` | ❌ | 10 | 检索的近邻数量 |
| `--num_threads` | ❌ | -1 | OpenMP 线程数（-1 为默认） |
| `--ef_search_values` | ❌ | "16,32,64,128,256" | 测试的 efSearch 值列表 |
| `--max_cpu_cores` | ❌ | 16 | 最大 CPU 核心数限制 |
| `--max_memory_gb` | ❌ | 64.0 | 最大内存使用量限制（GB） |
| `--enable_resource_monitoring` | ❌ | False | 启用实时资源监控 |

## 输出示例

```
Using default OpenMP threads: 256
Set CPU affinity to cores: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
Set FAISS OpenMP threads to: 16
Started resource monitoring (check interval: 1.0s)
Loading index with resource limits: 16C / 64.0GB
Loading pre-built index from /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
Index loaded in 5.52 seconds
Index type: IndexHNSWFlat
Index size: 10000000 vectors
Index dimension: 768
Memory usage after loading index: 31.06GB
No queries provided, generating synthetic queries...
Loaded 1000 queries with dimension 768
Starting time-limited benchmark for 30.0 seconds
Testing 5 parameter configurations
Using 1000 queries with k=10
Resource limits: 16C / 64.0GB
Initial memory usage: 31.06GB

Configuration 1/5: {'efSearch': 16}
  Search time: 15.77 ms
  QPS: 63400.21
  ms/query: 0.016
  Missing rate: 0.0000

Configuration 2/5: {'efSearch': 32}
  Search time: 14.91 ms
  QPS: 67077.74
  ms/query: 0.015
  Missing rate: 0.0000

...

============================================================
BENCHMARK SUMMARY
============================================================
Total configurations tested: 5
Actual duration: 0.12 seconds
QPS range: 26427.47 - 67077.74
Average QPS: 52072.34

Detailed Results:
------------------------------------------------------------
Config 1: efSearch=16
  QPS: 63400.21, ms/query: 0.016
Config 2: efSearch=32
  QPS: 67077.74, ms/query: 0.015
Config 3: efSearch=64
  QPS: 60582.44, ms/query: 0.017
Config 4: efSearch=128
  QPS: 42873.83, ms/query: 0.023
Config 5: efSearch=256
  QPS: 26427.47, ms/query: 0.038

============================================================
RESOURCE USAGE SUMMARY
============================================================
Config 1:
  Memory: 31.06GB -> 31.06GB (Δ+0.000GB)
  CPU: 1478.9%
  Memory usage: 48.5% of limit
Config 2:
  Memory: 31.06GB -> 31.21GB (Δ+0.150GB)
  CPU: 1478.9%
  Memory usage: 48.8% of limit
...

==================================================
RESOURCE USAGE SUMMARY
==================================================
Memory Usage: 31.21GB / 64.00GB (48.8%)
CPU Usage: 0.0%
CPU Cores Limit: 16
FAISS Threads: 16
==================================================
```

## 性能指标说明

- **QPS (Queries Per Second)**：每秒处理的查询数量
- **ms/query**：每个查询的平均延迟（毫秒）
- **Missing rate**：返回 -1 的比例（表示未找到足够的邻居）
- **Recall@1**：Top-1 召回率（需要 ground truth）
- **Recall@k**：Top-k 召回率（需要 ground truth）
- **Memory Usage**：内存使用量和使用率
- **CPU Usage**：CPU 使用率
- **Memory Delta**：每次搜索前后的内存变化

## 数据格式要求

### 查询向量文件 (.npy)
- 格式：numpy array，shape 为 (num_queries, 768)
- 数据类型：float32
- 维度：768（Cohere 嵌入维度）

### Ground Truth 文件 (.npy)
- 格式：numpy array，shape 为 (num_queries, k)
- 数据类型：int32 或 int64
- 内容：每个查询的真实最近邻索引

## 注意事项

1. **内存需求**：10M 向量的索引文件约 30GB，确保有足够内存
2. **加载时间**：首次加载大索引可能需要几秒到几十秒
3. **线程设置**：可以通过 `--num_threads` 调整并行度以优化性能
4. **参数调优**：efSearch 值越大通常召回率越高但速度越慢
5. **资源限制**：设置合理的 CPU 和内存限制以避免系统过载
6. **监控开销**：启用资源监控会有轻微的性能开销

## 与原始 bench_hnsw.py 的区别

| 特性 | bench_hnsw.py | bench_cohere_10m.py |
|------|---------------|---------------------|
| 数据集 | 固定 SIFT1M | 支持 Cohere 10M |
| 索引加载 | 从头构建 | 预加载现有索引 |
| 时间限制 | 无 | 支持指定时长 |
| 查询数据 | 固定数据集 | 自定义或自动生成 |
| 参数配置 | 硬编码 | 命令行可配置 |
| 输出格式 | 简单 | 详细的性能报告 |
| 资源限制 | 无 | CPU/内存限制 |
| 资源监控 | 无 | 实时监控 |

## 故障排除

### 常见错误

1. **索引文件不存在**
   ```
   FileNotFoundError: Index file not found: /path/to/index.faiss
   ```
   解决：检查索引文件路径是否正确

2. **查询向量维度不匹配**
   ```
   ValueError: Expected dimension 768, got 128
   ```
   解决：确保查询向量是 768 维的 Cohere 嵌入

3. **内存不足**
   ```
   MemoryError: Unable to allocate array
   ```
   解决：增加系统内存或使用较小的索引文件

### 性能优化建议

1. **调整线程数**：根据 CPU 核心数设置合适的线程数
2. **efSearch 调优**：在召回率和速度之间找到平衡点
3. **查询批次**：可以修改脚本支持更大的查询批次以提高吞吐量

## 扩展功能

脚本设计为模块化，可以轻松扩展：

- 添加新的索引类型支持
- 支持更多的搜索参数
- 添加更多的性能指标
- 支持分布式 benchmark

## 许可证

本工具遵循与 FAISS 相同的 MIT 许可证。
