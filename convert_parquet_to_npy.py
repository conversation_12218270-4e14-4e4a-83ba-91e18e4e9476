import pyarrow.parquet as pq
import numpy as np
import pandas as pd
import os

def parquet_to_numpy(path, max_rows=None):
    print(f"Loading {path}")
    df = pd.read_parquet(path)

    # 找到第一个 ndarray/list 类型的列作为向量列
    for col in df.columns:
        first_val = df[col].iloc[0]
        if isinstance(first_val, (list, np.ndarray)):
            vecs = df[col].tolist()
            arr = np.array(vecs, dtype=np.float32)
            break
    else:
        raise ValueError("没有找到嵌套向量列")

    if max_rows is not None:
        arr = arr[:max_rows]
    return arr

def convert_and_save(paths, output_file, max_rows=None):
    arrays = [parquet_to_numpy(p, max_rows) for p in paths]
    full_array = np.vstack(arrays)
    np.save(output_file, full_array)
    print(f"Saved {output_file}, shape={full_array.shape}, dtype={full_array.dtype}")

# 修改为你的路径
base_paths = [
    "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/shuffle_train-00-of-10.parquet",
    # 可加更多 shard
]

query_path = "/nas/yvan.chen/milvus/dataset/cohere/cohere_large_10m/test.parquet"

convert_and_save(base_paths, "base.npy")
convert_and_save([query_path], "query.npy")

