#!/usr/bin/env python3
# Copyright (c) Meta Platforms, Inc. and affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

"""
Benchmark script for Cohere 10M vectors with pre-built FAISS index support.
Supports time-limited benchmarking and custom query datasets.

Usage:
    python bench_cohere_10m.py --index_path /path/to/index.faiss --query_path /path/to/queries.npy --duration 30
"""

import argparse
import time
import sys
import os
import numpy as np
import faiss
from typing import Optional, Tuple, Dict, Any
import threading
import signal


class TimeoutException(Exception):
    """Exception raised when benchmark times out"""
    pass


class TimeLimitedBenchmark:
    """Context manager for time-limited benchmarking"""
    
    def __init__(self, duration_seconds: float):
        self.duration = duration_seconds
        self.start_time = None
        self.timeout_flag = threading.Event()
        self.timer = None
    
    def __enter__(self):
        self.start_time = time.time()
        self.timer = threading.Timer(self.duration, self._timeout)
        self.timer.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.timer:
            self.timer.cancel()
    
    def _timeout(self):
        self.timeout_flag.set()
    
    def is_timeout(self) -> bool:
        return self.timeout_flag.is_set()
    
    def elapsed_time(self) -> float:
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time


class CohereDataset:
    """Dataset class for Cohere 10M vectors"""
    
    def __init__(self, query_path: Optional[str] = None, 
                 query_vectors: Optional[np.ndarray] = None,
                 ground_truth: Optional[np.ndarray] = None):
        """
        Initialize Cohere dataset
        
        Args:
            query_path: Path to query vectors (.npy file)
            query_vectors: Query vectors as numpy array
            ground_truth: Ground truth nearest neighbors
        """
        self.d = 768  # Cohere embedding dimension
        self.metric = 'L2'  # Default metric
        
        if query_path is not None:
            self.xq = self._load_queries(query_path)
        elif query_vectors is not None:
            self.xq = query_vectors.astype('float32')
        else:
            # Generate synthetic queries if none provided
            print("No queries provided, generating synthetic queries...")
            self.xq = self._generate_synthetic_queries(1000)
        
        self.nq = len(self.xq)
        self.gt = ground_truth
        
        print(f"Loaded {self.nq} queries with dimension {self.d}")
    
    def _load_queries(self, query_path: str) -> np.ndarray:
        """Load query vectors from file"""
        if not os.path.exists(query_path):
            raise FileNotFoundError(f"Query file not found: {query_path}")
        
        print(f"Loading queries from {query_path}")
        queries = np.load(query_path)
        
        if queries.dtype != np.float32:
            queries = queries.astype('float32')
        
        if len(queries.shape) != 2:
            raise ValueError(f"Expected 2D array, got shape {queries.shape}")
        
        if queries.shape[1] != self.d:
            raise ValueError(f"Expected dimension {self.d}, got {queries.shape[1]}")
        
        return queries
    
    def _generate_synthetic_queries(self, nq: int) -> np.ndarray:
        """Generate synthetic query vectors for testing"""
        np.random.seed(42)  # For reproducibility
        return np.random.randn(nq, self.d).astype('float32')
    
    def get_queries(self) -> np.ndarray:
        """Return query vectors"""
        return self.xq
    
    def get_groundtruth(self, k: Optional[int] = None) -> Optional[np.ndarray]:
        """Return ground truth if available"""
        if self.gt is None:
            return None
        if k is not None and k < self.gt.shape[1]:
            return self.gt[:, :k]
        return self.gt


def load_prebuilt_index(index_path: str) -> faiss.Index:
    """
    Load a pre-built FAISS index from file
    
    Args:
        index_path: Path to the FAISS index file
        
    Returns:
        Loaded FAISS index
    """
    if not os.path.exists(index_path):
        raise FileNotFoundError(f"Index file not found: {index_path}")
    
    print(f"Loading pre-built index from {index_path}")
    start_time = time.time()
    
    # Load with memory mapping for large indices
    io_flag = faiss.IO_FLAG_READ_ONLY | faiss.IO_FLAG_MMAP
    index = faiss.read_index(index_path, io_flag)
    
    load_time = time.time() - start_time
    print(f"Index loaded in {load_time:.2f} seconds")
    print(f"Index type: {type(index).__name__}")
    print(f"Index size: {index.ntotal} vectors")
    print(f"Index dimension: {index.d}")
    
    return index


def evaluate_search_performance(index: faiss.Index, 
                               queries: np.ndarray,
                               k: int,
                               ground_truth: Optional[np.ndarray] = None,
                               search_params: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
    """
    Evaluate search performance for given queries
    
    Args:
        index: FAISS index
        queries: Query vectors
        k: Number of nearest neighbors to retrieve
        ground_truth: Ground truth for recall calculation
        search_params: Search parameters (e.g., nprobe, efSearch)
        
    Returns:
        Dictionary with performance metrics
    """
    nq = len(queries)
    
    # Set search parameters if provided
    if search_params:
        for param, value in search_params.items():
            try:
                if hasattr(index, param):
                    setattr(index, param, value)
                elif hasattr(index, 'hnsw') and hasattr(index.hnsw, param):
                    setattr(index.hnsw, param, value)
                elif hasattr(index, 'quantizer') and hasattr(index.quantizer, param):
                    setattr(index.quantizer, param, value)
                else:
                    print(f"Warning: Parameter {param} not found in index")
            except Exception as e:
                print(f"Warning: Failed to set parameter {param}={value}: {e}")
    
    # Perform search
    start_time = time.time()
    distances, indices = index.search(queries, k)
    search_time = time.time() - start_time
    
    # Calculate metrics
    metrics = {
        'search_time_ms': search_time * 1000,
        'queries_per_second': nq / search_time,
        'ms_per_query': (search_time * 1000) / nq,
        'total_queries': nq
    }
    
    # Calculate recall if ground truth is available
    if ground_truth is not None:
        recall_at_1 = np.mean(indices[:, 0:1] == ground_truth[:, 0:1])
        recall_at_k = np.mean([
            len(np.intersect1d(indices[i], ground_truth[i])) / k
            for i in range(min(nq, len(ground_truth)))
        ])
        metrics['recall_at_1'] = recall_at_1
        metrics[f'recall_at_{k}'] = recall_at_k
    
    # Calculate missing rate
    missing_rate = np.mean(indices == -1)
    metrics['missing_rate'] = missing_rate
    
    return metrics


def run_time_limited_benchmark(index: faiss.Index,
                              dataset: CohereDataset,
                              duration_seconds: float,
                              k: int = 10,
                              search_params_list: Optional[list] = None) -> Dict[str, Any]:
    """
    Run time-limited benchmark with multiple search parameter configurations

    Args:
        index: FAISS index
        dataset: Dataset containing queries
        duration_seconds: Maximum duration for benchmark
        k: Number of nearest neighbors to retrieve
        search_params_list: List of search parameter configurations to test

    Returns:
        Dictionary with benchmark results
    """
    if search_params_list is None:
        # Default HNSW search parameters
        search_params_list = [
            {'efSearch': 16},
            {'efSearch': 32},
            {'efSearch': 64},
            {'efSearch': 128},
            {'efSearch': 256}
        ]

    queries = dataset.get_queries()
    ground_truth = dataset.get_groundtruth(k)

    results = {
        'total_duration': duration_seconds,
        'configurations': [],
        'summary': {}
    }

    print(f"Starting time-limited benchmark for {duration_seconds} seconds")
    print(f"Testing {len(search_params_list)} parameter configurations")
    print(f"Using {len(queries)} queries with k={k}")

    with TimeLimitedBenchmark(duration_seconds) as timer:
        for i, search_params in enumerate(search_params_list):
            if timer.is_timeout():
                print(f"Timeout reached, stopping at configuration {i}")
                break

            print(f"\nConfiguration {i+1}/{len(search_params_list)}: {search_params}")

            try:
                # Run evaluation
                metrics = evaluate_search_performance(
                    index, queries, k, ground_truth, search_params
                )

                config_result = {
                    'config_id': i,
                    'search_params': search_params,
                    'metrics': metrics,
                    'elapsed_time': timer.elapsed_time()
                }

                results['configurations'].append(config_result)

                # Print results
                print(f"  Search time: {metrics['search_time_ms']:.2f} ms")
                print(f"  QPS: {metrics['queries_per_second']:.2f}")
                print(f"  ms/query: {metrics['ms_per_query']:.3f}")
                if 'recall_at_1' in metrics:
                    print(f"  Recall@1: {metrics['recall_at_1']:.4f}")
                if f'recall_at_{k}' in metrics:
                    print(f"  Recall@{k}: {metrics[f'recall_at_{k}']:.4f}")
                print(f"  Missing rate: {metrics['missing_rate']:.4f}")

            except Exception as e:
                print(f"Error in configuration {i}: {e}")
                continue

    # Calculate summary statistics
    if results['configurations']:
        all_qps = [config['metrics']['queries_per_second'] for config in results['configurations']]
        all_recall_1 = [config['metrics'].get('recall_at_1', 0) for config in results['configurations']]

        results['summary'] = {
            'total_configurations_tested': len(results['configurations']),
            'max_qps': max(all_qps),
            'min_qps': min(all_qps),
            'avg_qps': sum(all_qps) / len(all_qps),
            'max_recall_at_1': max(all_recall_1) if all_recall_1[0] != 0 else None,
            'actual_duration': timer.elapsed_time()
        }

    return results


def print_benchmark_summary(results: Dict[str, Any]):
    """Print a summary of benchmark results"""
    print("\n" + "="*60)
    print("BENCHMARK SUMMARY")
    print("="*60)

    summary = results['summary']
    print(f"Total configurations tested: {summary['total_configurations_tested']}")
    print(f"Actual duration: {summary['actual_duration']:.2f} seconds")
    print(f"QPS range: {summary['min_qps']:.2f} - {summary['max_qps']:.2f}")
    print(f"Average QPS: {summary['avg_qps']:.2f}")

    if summary.get('max_recall_at_1') is not None:
        print(f"Max Recall@1: {summary['max_recall_at_1']:.4f}")

    print("\nDetailed Results:")
    print("-" * 60)
    for config in results['configurations']:
        params_str = ", ".join([f"{k}={v}" for k, v in config['search_params'].items()])
        metrics = config['metrics']
        print(f"Config {config['config_id']+1}: {params_str}")
        print(f"  QPS: {metrics['queries_per_second']:.2f}, "
              f"ms/query: {metrics['ms_per_query']:.3f}")
        if 'recall_at_1' in metrics:
            print(f"  Recall@1: {metrics['recall_at_1']:.4f}")


def main():
    """Main function to run the benchmark"""
    parser = argparse.ArgumentParser(
        description="Benchmark Cohere 10M vectors with pre-built FAISS index"
    )

    parser.add_argument(
        '--index_path',
        type=str,
        required=True,
        help='Path to pre-built FAISS index file'
    )

    parser.add_argument(
        '--query_path',
        type=str,
        help='Path to query vectors (.npy file). If not provided, synthetic queries will be generated'
    )

    parser.add_argument(
        '--ground_truth_path',
        type=str,
        help='Path to ground truth file (.npy file)'
    )

    parser.add_argument(
        '--duration',
        type=float,
        default=30.0,
        help='Benchmark duration in seconds (default: 30)'
    )

    parser.add_argument(
        '--k',
        type=int,
        default=10,
        help='Number of nearest neighbors to retrieve (default: 10)'
    )

    parser.add_argument(
        '--num_threads',
        type=int,
        default=-1,
        help='Number of OpenMP threads (-1 for default)'
    )

    parser.add_argument(
        '--ef_search_values',
        type=str,
        default="16,32,64,128,256",
        help='Comma-separated efSearch values to test (default: 16,32,64,128,256)'
    )

    args = parser.parse_args()

    # Set number of threads
    if args.num_threads > 0:
        faiss.omp_set_num_threads(args.num_threads)
        print(f"Set OpenMP threads to {args.num_threads}")
    else:
        print(f"Using default OpenMP threads: {faiss.omp_get_max_threads()}")

    try:
        # Load index
        index = load_prebuilt_index(args.index_path)

        # Load ground truth if provided
        ground_truth = None
        if args.ground_truth_path:
            if os.path.exists(args.ground_truth_path):
                print(f"Loading ground truth from {args.ground_truth_path}")
                ground_truth = np.load(args.ground_truth_path)
            else:
                print(f"Warning: Ground truth file not found: {args.ground_truth_path}")

        # Create dataset
        dataset = CohereDataset(
            query_path=args.query_path,
            ground_truth=ground_truth
        )

        # Parse efSearch values
        ef_search_values = [int(x.strip()) for x in args.ef_search_values.split(',')]
        search_params_list = [{'efSearch': ef} for ef in ef_search_values]

        # Run benchmark
        results = run_time_limited_benchmark(
            index=index,
            dataset=dataset,
            duration_seconds=args.duration,
            k=args.k,
            search_params_list=search_params_list
        )

        # Print summary
        print_benchmark_summary(results)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
