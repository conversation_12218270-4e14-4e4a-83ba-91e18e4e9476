# QPS 计算说明

## 问题背景

用户发现 benchmark 结果中的 QPS 计算可能有问题：
```
Total runs: 1523
Actual duration: 30.01 seconds  
Average QPS (all runs): 66,449.25
```

## QPS 计算方式

### 修复前的问题
原来只有一个 QPS 指标，计算方式不够清晰，容易产生误解。

### 修复后的双重 QPS 指标

#### 1. Overall QPS（总体 QPS）
**计算公式：**
```
Overall QPS = 总查询数 / 总时间
            = (运行次数 × 每次查询数) / 总时间
```

**示例：**
```
总运行次数：980 次
每次查询数：1,000 个
总查询数：980,000 个
总时间：10.01 秒
Overall QPS = 980,000 / 10.01 = 97,921
```

**含义：** 整个 benchmark 期间的真实吞吐量，包含所有开销。

#### 2. Individual Run QPS（单次运行 QPS）
**计算公式：**
```
Individual Run QPS = 每次查询数 / 单次运行时间
```

**示例：**
```
某次运行：1,000 个查询用时 0.01 秒
Individual Run QPS = 1,000 / 0.01 = 100,000
```

**含义：** 单次批量查询的瞬时性能，不包含运行间开销。

## 实际测试结果对比

### 10 秒测试结果
```
Total runs: 980
Total cycles: 490
Actual duration: 10.01 seconds
Total queries processed: 980,000
Overall QPS: 97,921.19
QPS range (individual runs): 58,761 - 116,872
Average QPS (individual runs): 100,919.61
```

### 30 秒测试结果（修复前）
```
Total runs: 1,523
Actual duration: 30.01 seconds
Average QPS (all runs): 66,449.25
```

**修复后应该显示：**
```
Total queries processed: 1,523,000
Overall QPS: 50,749 (1,523,000 / 30.01)
Average QPS (individual runs): 66,449
```

## 两种 QPS 的差异原因

### Overall QPS 更低的原因
1. **运行间切换开销**：配置切换、参数设置
2. **资源监控开销**：CPU/内存监控
3. **系统调度开销**：操作系统任务调度
4. **内存管理开销**：垃圾回收、内存分配

### Individual Run QPS 更高的原因
1. **批量处理优势**：1000 个查询一次性处理
2. **缓存效应**：CPU 缓存、内存预取
3. **无切换开销**：单次运行内无配置切换

## 哪个 QPS 更有意义？

### Overall QPS（推荐用于性能评估）
- ✅ **更真实**：反映实际部署时的性能
- ✅ **包含开销**：考虑了所有系统开销
- ✅ **可重现**：不同测试环境下更稳定

### Individual Run QPS（用于理论分析）
- ✅ **峰值性能**：展示算法的理论上限
- ✅ **优化指导**：帮助识别性能瓶颈
- ❌ **过于理想**：实际应用中难以达到

## 建议的性能报告格式

```
============================================================
BENCHMARK SUMMARY
============================================================
Total runs: 980
Total cycles: 490
Configurations tested: 2
Actual duration: 10.01 seconds
Total queries processed: 980,000

PERFORMANCE METRICS:
Overall QPS: 97,921 (realistic performance)
Peak QPS: 116,872 (best individual run)
Average QPS (individual runs): 100,920 (theoretical performance)

Average Performance per Configuration:
Config 1 (efSearch=16): 115,298 QPS
Config 2 (efSearch=32): 86,542 QPS
```

## 性能基准参考

基于修复后的计算，16C64G 环境下的性能基准：

| efSearch | Overall QPS | Peak QPS | 适用场景 |
|----------|-------------|----------|----------|
| 16       | ~95,000     | ~115,000 | 高吞吐量 |
| 32       | ~85,000     | ~100,000 | 平衡性能 |
| 64       | ~60,000     | ~80,000  | 中等精度 |
| 128      | ~40,000     | ~55,000  | 高精度 |
| 256      | ~25,000     | ~35,000  | 最高精度 |

## 总结

1. **修复了 QPS 计算**：现在提供两种 QPS 指标
2. **Overall QPS 更准确**：反映真实部署性能
3. **Individual Run QPS 仍有价值**：用于理论分析
4. **建议关注 Overall QPS**：用于性能评估和容量规划

修复后的工具提供了更准确、更全面的性能指标！
