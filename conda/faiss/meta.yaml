# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

{% set version = environ.get('GIT_DESCRIBE_TAG').lstrip('v') %}
{% set suffix = "_nightly" if environ.get('PACKAGE_TYPE') == 'nightly' else "" %}
{% set number = GIT_DESCRIBE_NUMBER %}

package:
  name: faiss-pkg
  version: {{ version }}

build:
  number: {{ number }}

about:
  home: https://github.com/facebookresearch/faiss
  license: MIT
  license_family: MIT
  license_file: LICENSE
  summary: A library for efficient similarity search and clustering of dense vectors.

source:
  git_url: ../../

outputs:
  - name: libfaiss
    script: build-lib.sh  # [x86_64 and not win and not osx]
    script: build-lib-osx.sh  # [x86_64 and osx]
    script: build-lib-arm64.sh  # [not x86_64]
    script: build-lib.bat  # [win]
    build:
      string: "py{{ PY_VER }}_h{{ PKG_HASH }}_{{ number }}_cpu{{ suffix }}"
      run_exports:
        - {{ pin_compatible('libfaiss', exact=True) }}
    requirements:
      build:
        - python {{ python }}
        - {{ compiler('cxx') }}
        - sysroot_linux-64 =2.17 # [linux64]
        - llvm-openmp  # [osx or linux64]
        - cmake >=3.24.0
        - make =4.2 # [not win and not (osx and arm64)]
        - make =4.4 # [osx and arm64]
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl-devel =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl-devel >=2023.2.0  # [x86_64 and not win]
        - mkl-devel =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
      host:
        - python {{ python }}
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl >=2023.2.0  # [x86_64 and not win]
        - mkl =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
        - openblas =0.3.30 # [not x86_64]
      run:
        - python {{ python }}
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl >=2023.2.0  # [x86_64 and not win]
        - mkl =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
        - openblas =0.3.30 # [not x86_64]
    test:
      requires:
        - conda-build =25.1.2
      commands:
        - test -f $PREFIX/lib/libfaiss$SHLIB_EXT       # [not win]
        - test -f $PREFIX/lib/libfaiss_avx2$SHLIB_EXT  # [x86_64 and not win]
        - conda inspect linkages -p $PREFIX $PKG_NAME  # [not win]
        - conda inspect objects -p $PREFIX $PKG_NAME   # [osx]

  - name: faiss-cpu
    script: build-pkg.sh  # [x86_64 and not win and not osx]
    script: build-pkg-osx.sh  # [x86_64 and osx]
    script: build-pkg-arm64.sh # [not x86_64]
    script: build-pkg.bat  # [win]
    build:
      string: "py{{ PY_VER }}_h{{ PKG_HASH }}_{{ number }}_cpu{{ suffix }}"
    requirements:
      build:
        - python {{ python }}
        - {{ compiler('cxx') }}
        - sysroot_linux-64 =2.17 # [linux64]
        - swig =4.0
        - cmake >=3.24.0
        - make =4.2 # [not win and not (osx and arm64)]
        - make =4.4 # [osx and arm64]
        - _openmp_mutex =4.5=2_kmp_llvm  # [x86_64 and not win]
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl >=2023.2.0  # [x86_64 and not win]
        - mkl =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
      host:
        - python {{ python }}
        - numpy >=1.19,<2
        - {{ pin_subpackage('libfaiss', exact=True) }}
        - _openmp_mutex =4.5=2_kmp_llvm  # [x86_64 and not win]
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl >=2023.2.0  # [x86_64 and not win]
        - mkl =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
      run:
        - python {{ python }}
        - numpy >=1.19,<2
        - packaging
        - {{ pin_subpackage('libfaiss', exact=True) }}
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl >=2023.2.0  # [x86_64 and not win]
        - mkl =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
    test:
      requires:
        - numpy >=1.19,<2
        - scipy
        - pytorch <2.5
        {% if PY_VER == '3.9' or PY_VER == '3.10' or PY_VER == '3.11' %}
        - mkl =2023.0  # [x86_64]
        - python_abi <3.12
        {% elif PY_VER == '3.12' %}
        - mkl >=2023.2.0  # [x86_64 and not win]
        - mkl =2023.1.0  # [x86_64 and win]
        - python_abi =3.12
        {% endif %}
      commands:
        - python -X faulthandler -m unittest discover -v -s tests/ -p "test_*"
        - python -X faulthandler -m unittest discover -v -s tests/ -p "torch_*"
        - sh test_cpu_dispatch.sh  # [linux64]
      files:
        - test_cpu_dispatch.sh  # [linux64]
      source_files:
        - tests/
