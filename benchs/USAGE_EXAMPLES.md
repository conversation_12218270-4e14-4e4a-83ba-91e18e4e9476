# FAISS Cohere 10M Benchmark 使用示例

## 基本使用

### 1. 标准 30 秒 benchmark（16C64G 限制）
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64
```

### 2. 安静模式（减少输出）
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --quiet
```

### 3. 启用资源监控
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --enable_resource_monitoring \
    --quiet
```

## 不同资源限制配置

### 4. 8 核心 32GB 限制
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 8 \
    --max_memory_gb 32 \
    --quiet
```

### 5. 4 核心 16GB 限制
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 4 \
    --max_memory_gb 16 \
    --quiet
```

## 自定义参数配置

### 6. 自定义 efSearch 值
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --ef_search_values "8,16,32,64,128,256,512" \
    --quiet
```

### 7. 自定义查询数据和 ground truth
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --query_path /path/to/your/queries.npy \
    --ground_truth_path /path/to/ground_truth.npy \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --k 100 \
    --quiet
```

## 快速测试

### 8. 快速 5 秒测试
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 5 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --ef_search_values "16,32" \
    --quiet
```

### 9. 单配置测试
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 10 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --ef_search_values "64" \
    --quiet
```

## 输出对比

### 标准模式输出（冗长）
```
Configuration 1/5: {'efSearch': 16}
  Cycle 0: QPS: 38712.84, ms/query: 0.026

Configuration 2/5: {'efSearch': 32}
  Cycle 0: QPS: 60983.22, ms/query: 0.016

Completed first cycle, continuing for 10.0 more seconds...
Completed 50 cycles, 100 total runs...
  Cycle 50: QPS: 105257.58, ms/query: 0.010
  Cycle 50: QPS: 78804.75, ms/query: 0.013
...
```

### 安静模式输出（简洁）
```
Configuration 1/2: {'efSearch': 16}

Configuration 2/2: {'efSearch': 32}
Progress: 100 cycles, 200 total runs...
Progress: 200 cycles, 400 total runs...
Progress: 300 cycles, 600 total runs...
Progress: 400 cycles, 800 total runs...

Time limit reached (10.00s), stopping benchmark
```

## 性能基准参考

基于 16C64G 配置的典型性能：

| efSearch | 平均 QPS | 延迟 (ms) | 适用场景 |
|----------|----------|-----------|----------|
| 16       | ~113,000 | 0.009     | 高吞吐量 |
| 32       | ~86,000  | 0.012     | 平衡性能 |
| 64       | ~63,000  | 0.016     | 中等精度 |
| 128      | ~43,000  | 0.023     | 高精度 |
| 256      | ~27,000  | 0.037     | 最高精度 |

## 故障排除

### 常见问题

1. **索引文件不存在**
   ```bash
   ls -la /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index
   ```

2. **内存不足**
   - 减少 `--max_memory_gb` 值
   - 或增加系统内存

3. **CPU 核心数过多**
   - 检查系统核心数：`nproc`
   - 设置合适的 `--max_cpu_cores`

4. **性能异常**
   - 检查系统负载：`htop`
   - 确保没有其他高负载进程

### 调试模式

启用详细输出（不使用 --quiet）：
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 10 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --enable_resource_monitoring
```

## 推荐配置

### 生产环境
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --ef_search_values "16,32,64,128" \
    --quiet
```

### 开发测试
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 5 \
    --max_cpu_cores 8 \
    --max_memory_gb 32 \
    --ef_search_values "16,64" \
    --quiet
```

### 性能调优
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 60 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --ef_search_values "8,12,16,20,24,32,48,64" \
    --enable_resource_monitoring \
    --quiet
```
