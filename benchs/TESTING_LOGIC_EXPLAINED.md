# FAISS Benchmark 脚本测试逻辑详解

## 1. 整体测试架构

### 1.1 测试流程图
```
启动 → 资源限制 → 加载索引 → 准备数据 → 循环测试 → 统计结果
  ↓        ↓         ↓        ↓        ↓        ↓
参数解析  CPU/内存   预构建    查询向量   时间控制   性能报告
         限制      索引      生成/加载   多轮测试   QPS计算
```

### 1.2 核心组件
1. **ResourceManager**: 资源限制和监控
2. **TimeLimitedBenchmark**: 时间控制
3. **CohereDataset**: 数据管理
4. **evaluate_search_performance**: 性能评估
5. **run_time_limited_benchmark**: 主测试逻辑

## 2. 详细测试逻辑

### 2.1 初始化阶段

```python
# 1. 资源限制设置
resource_manager = ResourceManager(max_cpu_cores=16, max_memory_gb=64)
- 设置 CPU 亲和性到指定核心 [0-15]
- 限制 FAISS OpenMP 线程数为 16
- 启动后台内存监控线程

# 2. 索引加载
index = load_prebuilt_index(index_path)
- 使用内存映射加载大索引文件
- 解析 HNSW 参数 (M=30, efConstruction=360)
- 显示索引信息 (10M vectors, 768d)

# 3. 数据准备
dataset = CohereDataset(query_path, ground_truth)
- 加载查询向量 (1000 个 768 维向量)
- 如果没有提供，生成合成查询向量
- 加载 ground truth (如果有)
```

### 2.2 核心测试循环

```python
# 时间限制循环 (30 秒)
start_time = time.time()
config_cycle = 0
total_runs = 0

while time.time() - start_time < duration_seconds:
    # 遍历所有配置 [efSearch: 16, 32, 64, 128, 256]
    for i, search_params in enumerate(search_params_list):
        
        # 1. 时间检查
        current_time = time.time() - start_time
        if current_time >= duration_seconds:
            break
            
        # 2. 单次性能测试
        metrics = evaluate_search_performance(
            index, queries, k, ground_truth, search_params
        )
        
        # 3. 记录结果
        results['configurations'].append({
            'config_id': i,
            'cycle': config_cycle,
            'search_params': search_params,
            'metrics': metrics,
            'elapsed_time': current_time
        })
        
    config_cycle += 1
```

### 2.3 单次性能测试逻辑

```python
def evaluate_search_performance(index, queries, k, ground_truth, search_params):
    # 1. 设置搜索参数
    for param, value in search_params.items():
        if hasattr(index, 'hnsw') and hasattr(index.hnsw, param):
            setattr(index.hnsw, param, value)  # 设置 efSearch
    
    # 2. 执行搜索
    start_time = time.time()
    distances, indices = index.search(queries, k)  # 1000 queries × k neighbors
    search_time = time.time() - start_time
    
    # 3. 计算性能指标
    metrics = {
        'search_time_ms': search_time * 1000,
        'queries_per_second': len(queries) / search_time,  # 1000 / search_time
        'ms_per_query': (search_time * 1000) / len(queries),
        'total_queries': len(queries)
    }
    
    # 4. 计算召回率 (如果有 ground truth)
    if ground_truth is not None:
        recall_at_1 = np.mean(indices[:, 0:1] == ground_truth[:, 0:1])
        recall_at_k = np.mean([...])  # Top-k 召回率
    
    return metrics
```

## 3. 时间控制机制

### 3.1 双重时间检查
```python
# 外层循环检查
while time.time() - start_time < duration_seconds:
    
    # 内层配置检查  
    for i, search_params in enumerate(search_params_list):
        current_time = time.time() - start_time
        if current_time >= duration_seconds:
            break  # 立即停止
```

### 3.2 循环执行逻辑
```
Cycle 0: Config1 → Config2 → Config3 → Config4 → Config5
Cycle 1: Config1 → Config2 → Config3 → Config4 → Config5  
Cycle 2: Config1 → Config2 → Config3 → Config4 → Config5
...
直到 30 秒时间到达
```

**示例执行序列（30秒）：**
- 每个配置执行时间 ~0.02 秒
- 一轮 5 个配置 ~0.1 秒  
- 30 秒内可执行 ~300 轮
- 总测试次数：300 轮 × 5 配置 = 1500 次

## 4. 性能指标计算

### 4.1 Individual Run QPS (单次运行 QPS)
```python
# 每次测试的瞬时性能
queries_per_second = 1000 / search_time

# 示例：
# search_time = 0.01 秒
# QPS = 1000 / 0.01 = 100,000
```

### 4.2 Overall QPS (总体 QPS)
```python
# 整个测试期间的真实吞吐量
total_queries = total_runs * 1000
overall_qps = total_queries / actual_duration

# 示例：
# total_runs = 1500, actual_duration = 30.0s
# overall_qps = 1,500,000 / 30.0 = 50,000
```

### 4.3 为什么两个 QPS 不同？

**Individual Run QPS 更高的原因：**
1. **批量处理优势**: 1000 个查询一次性处理
2. **缓存效应**: CPU 缓存、内存预取优化
3. **无切换开销**: 单次运行内无配置切换

**Overall QPS 更低但更真实：**
1. **包含切换开销**: 配置切换、参数设置
2. **资源监控开销**: CPU/内存监控
3. **系统调度开销**: 操作系统任务调度

## 5. 资源监控逻辑

### 5.1 实时监控
```python
def monitor():
    while self.monitoring:
        if not self.check_memory_limit():
            print("WARNING: Memory limit exceeded!")
        time.sleep(1.0)  # 每秒检查一次
```

### 5.2 资源记录
```python
# 只在特定时机记录资源使用
if resource_manager and (config_cycle == 0 or config_cycle % 50 == 0):
    pre_search_stats = resource_manager.get_resource_stats()
    # 执行搜索
    post_search_stats = resource_manager.get_resource_stats()
    # 记录资源变化
```

## 6. 结果统计逻辑

### 6.1 按配置分组
```python
config_groups = {}
for config in results['configurations']:
    config_id = config['config_id']
    if config_id not in config_groups:
        config_groups[config_id] = []
    config_groups[config_id].append(config['metrics']['queries_per_second'])

# 计算每个配置的平均性能
avg_qps_per_config = {
    cid: sum(qps_list)/len(qps_list) 
    for cid, qps_list in config_groups.items()
}
```

### 6.2 统计汇总
```python
results['summary'] = {
    'total_runs': len(results['configurations']),      # 1500
    'total_cycles': config_cycle,                       # 300
    'configurations_tested': len(config_groups),       # 5
    'overall_qps': total_queries / actual_duration,    # 50,000
    'avg_qps_individual_runs': sum(all_qps) / len(all_qps),  # 100,000
    'actual_duration': actual_duration                 # 30.02
}
```

## 7. 测试逻辑的优势

### 7.1 时间精确控制
- 双重时间检查确保精确的 30 秒测试
- 避免测试时间过长或过短

### 7.2 统计学意义
- 大量重复测试（1500+ 次）提供统计学意义
- 减少单次测试的随机误差

### 7.3 真实性能评估
- Overall QPS 反映真实部署性能
- 包含所有系统开销

### 7.4 资源可控
- CPU 核心数和内存使用严格限制
- 避免系统过载影响测试结果

## 8. 测试结果解读

### 8.1 性能指标含义
- **Overall QPS**: 生产环境预期性能
- **Individual Run QPS**: 理论峰值性能
- **CPU 使用率**: 资源利用效率
- **内存使用**: 资源消耗情况

### 8.2 配置对比
- 不同 efSearch 值的性能权衡
- 召回率 vs 速度的平衡点
- 资源使用效率对比

这个测试逻辑设计确保了准确、可重复、有统计学意义的性能评估！
