name: Build
on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
  push:
    tags:
      - 'v*'
jobs:
  build-pull-request:
    uses: ./.github/workflows/build-pull-request.yml
  build-release:
    uses: ./.github/workflows/build-release.yml
    secrets:
      ANACONDA_API_TOKEN: ${{ secrets.ANACONDA_API_TOKEN }}
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
