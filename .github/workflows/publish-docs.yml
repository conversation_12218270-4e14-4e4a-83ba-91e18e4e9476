name: Publish Docs
on:
  page_build:
    branches:
      - gh-pages
    paths-ignore:
      - 'docs/**'
  workflow_run:
    workflows: [update-doxygen]
    types:
      - completed
jobs:
  build_and_publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Checkout gh-pages
        run: |
          git fetch origin gh-pages
          git checkout gh-pages
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Generate html
        run: |
          make html
          git rm -rf docs
          mv _build/html docs
          touch docs/.nojekyll
      - name: Push changes
        run: |
          git config --global user.email "$<EMAIL>"
          git config --global user.name "$GITHUB_ACTOR"
          git add docs
          if [ -n "$(git status --porcelain)" ]
          then
            git commit docs -m "Sphinx rebuild ($(git rev-parse --short gh-pages))."
            git push origin gh-pages
          fi
