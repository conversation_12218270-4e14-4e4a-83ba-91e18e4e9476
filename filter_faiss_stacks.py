#!/usr/bin/env python3
import sys
import re

def filter_stack_line(line):
    """过滤掉重复的Python解释器调用，保留FAISS相关的调用"""
    parts = line.split(';')
    filtered_parts = []
    prev_pyeval = False
    
    for part in parts:
        # 如果是PyEval_EvalFrameDefault，只保留第一个
        if 'PyEval_EvalFrameDefault' in part:
            if not prev_pyeval:
                filtered_parts.append('PyEval_EvalFrameDefault')
                prev_pyeval = True
        else:
            prev_pyeval = False
            # 保留FAISS相关的函数
            if any(keyword in part for keyword in ['faiss', 'IndexFlat', 'search', 'knn', '_swigfaiss']):
                filtered_parts.append(part)
            # 保留其他重要的系统调用
            elif any(keyword in part for keyword in ['main', 'start_thread', 'clone']):
                filtered_parts.append(part)
            # 简化Python内部调用
            elif 'python' in part.lower() and '[unknown]' not in part:
                filtered_parts.append(part.split('(')[0])  # 只保留函数名
    
    return ';'.join(filtered_parts)

for line in sys.stdin:
    line = line.strip()
    if line and not line.startswith('#'):
        parts = line.split(' ')
        if len(parts) >= 2:
            stack = parts[0]
            count = parts[1]
            filtered_stack = filter_stack_line(stack)
            if filtered_stack:
                print(f"{filtered_stack} {count}")
