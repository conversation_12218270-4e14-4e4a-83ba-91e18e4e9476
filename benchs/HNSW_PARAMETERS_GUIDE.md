# FAISS HNSW 参数详解与配置指南

## 预构建索引的参数

从 `/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index` 分析得到：

```
HNSW Parameters:
  M (connections per node): 30
  efConstruction: 360
  efSearch: 16 (默认值)
  max_level: 5
  entry_point: 2646813
  levels: 0 to 3
  level distribution: {1: 973, 2: 26, 3: 1}
```

## HNSW 参数详解

### 1. M (连接数)
- **当前值**: 30
- **作用**: 每个节点在 HNSW 图中的最大连接数
- **影响**: 
  - 更高的 M → 更好的召回率，但更大的内存占用
  - 更低的 M → 更少的内存，但可能降低召回率
- **设置时机**: 索引构建时（无法在搜索时修改）
- **典型值**: 16-64

### 2. efConstruction (构建时搜索宽度)
- **当前值**: 360
- **作用**: 构建索引时的搜索候选数量
- **影响**: 
  - 更高的值 → 更好的图质量和召回率，但构建时间更长
  - 更低的值 → 更快的构建，但可能影响图质量
- **设置时机**: 索引构建时（无法在搜索时修改）
- **典型值**: 200-500

### 3. efSearch (搜索时搜索宽度)
- **当前值**: 16（默认），可在搜索时调整
- **作用**: 搜索时的候选数量
- **影响**: 
  - 更高的值 → 更好的召回率，但搜索时间更长
  - 更低的值 → 更快的搜索，但可能降低召回率
- **设置时机**: 搜索时（可动态调整）
- **典型值**: 16-512

## 你提到的参数配置

### 目标配置
- **k = 100**: 检索 100 个最近邻
- **ef ≈ 120**: efSearch = 120
- **--m 30**: M = 30 ✅（预构建索引已设置）
- **--ef-construction 360**: efConstruction = 360 ✅（预构建索引已设置）
- **--ef-search 100**: efSearch = 100

### 当前支持情况

| 参数 | 预构建索引值 | 目标值 | 状态 | 说明 |
|------|-------------|--------|------|------|
| M | 30 | 30 | ✅ 匹配 | 索引构建时已设置 |
| efConstruction | 360 | 360 | ✅ 匹配 | 索引构建时已设置 |
| efSearch | 16 | 100-120 | ✅ 可调整 | 搜索时动态设置 |
| k | 10 | 100 | ✅ 可调整 | 搜索时设置 |

## 使用你的参数配置

### 基本配置（k=100, efSearch=120）
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --k 100 \
    --ef_search_values "120" \
    --quiet
```

### 测试多个 efSearch 值
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --k 100 \
    --ef_search_values "100,110,120,130,140" \
    --quiet
```

### 使用自定义参数（JSON 格式）
```bash
python3 benchs/bench_cohere_10m.py \
    --index_path /home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --k 100 \
    --custom_search_params '[{"efSearch": 120}]' \
    --quiet
```

## 性能基准（你的配置）

基于测试结果：

### k=100, efSearch=120 配置
```
Total queries processed: 1,332,000
Overall QPS: 44,400 (30 秒测试)
Average QPS (individual runs): 44,804
CPU Usage: 92.6%
Memory Usage: 31.21GB / 64GB (48.8%)
```

### 性能对比（k=10 vs k=100）

| 配置 | k | efSearch | Overall QPS | 说明 |
|------|---|----------|-------------|------|
| 默认 | 10 | 64 | ~60,000 | 标准配置 |
| 你的配置 | 100 | 120 | ~44,400 | 更高精度 |

**性能下降原因**：
1. **k=100**: 需要返回更多结果，计算开销增加
2. **efSearch=120**: 搜索更多候选，提高精度但降低速度

## 参数调优建议

### 1. 平衡性能和精度
```bash
# 高性能配置
--k 10 --ef_search_values "64"

# 平衡配置  
--k 50 --ef_search_values "100"

# 高精度配置（你的需求）
--k 100 --ef_search_values "120"

# 最高精度配置
--k 100 --ef_search_values "200"
```

### 2. efSearch 与 k 的关系
- **推荐**: efSearch ≥ k
- **最佳**: efSearch = 1.2 * k 到 2 * k
- **你的配置**: efSearch=120, k=100 ✅（比例 1.2:1，合理）

### 3. 性能优化
```bash
# 如果需要更高性能，可以降低 efSearch
--k 100 --ef_search_values "100"  # 可能略微降低精度

# 如果需要更高精度，可以提高 efSearch  
--k 100 --ef_search_values "150"  # 进一步提高精度
```

## 完整的测试脚本

```bash
#!/bin/bash
# 测试你的参数配置

INDEX_PATH="/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index"

echo "=== 测试你的配置: k=100, efSearch=120 ==="
python3 benchs/bench_cohere_10m.py \
    --index_path $INDEX_PATH \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --k 100 \
    --ef_search_values "120" \
    --enable_resource_monitoring \
    --quiet

echo "=== 对比测试: 不同 efSearch 值 ==="
python3 benchs/bench_cohere_10m.py \
    --index_path $INDEX_PATH \
    --duration 30 \
    --max_cpu_cores 16 \
    --max_memory_gb 64 \
    --k 100 \
    --ef_search_values "100,110,120,130,140" \
    --quiet
```

## 总结

1. **预构建索引完全匹配你的需求**：M=30, efConstruction=360
2. **可以动态调整搜索参数**：k=100, efSearch=120
3. **性能表现良好**：44,400 QPS，CPU 使用率 92.6%
4. **参数配置合理**：efSearch/k 比例为 1.2:1

你的参数配置是合理的，预构建索引的参数也完全符合你的要求！
